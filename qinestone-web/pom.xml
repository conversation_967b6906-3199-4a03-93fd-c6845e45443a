<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qinestone</groupId>
        <artifactId>qinestone-edu</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>qinestone-web</artifactId>
    <name>qinestone-web</name>
    <description>Web控制层 - Controller、拦截器、过滤器等</description>

    <dependencies>
        <!-- 业务逻辑层 -->
        <dependency>
            <groupId>com.qinestone</groupId>
            <artifactId>qinestone-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- 校验框架 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>

</project>
