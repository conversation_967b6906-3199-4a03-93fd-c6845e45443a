package com.qinestone.web.controller;

import com.qinestone.common.annotation.ResponseResult;
import com.qinestone.common.context.UserContext;
import com.qinestone.common.result.Result;
import com.qinestone.entity.dto.LoginDTO;
import com.qinestone.entity.system.SysUser;
import com.qinestone.service.system.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Tag(name = "认证管理", description = "用户登录、登出等认证相关接口")
@RestController
@RequestMapping("/api/auth")
@ResponseResult
public class AuthController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户名密码登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Validated @RequestBody LoginDTO loginDTO) {
        log.info("用户登录请求: {}", loginDTO.getUsername());
        
        // 执行登录
        String token = sysUserService.login(loginDTO.getUsername(), loginDTO.getPassword());
        
        // 获取用户信息
        SysUser user = sysUserService.getByUsername(loginDTO.getUsername());
        
        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("token", token);
        data.put("userInfo", buildUserInfo(user));
        
        return Result.success("登录成功", data);
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出", description = "退出登录")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        sysUserService.logout(token);
        log.info("用户登出成功");
        return Result.success("登出成功");
    }

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/userinfo")
    public Result<Map<String, Object>> getUserInfo() {
        SysUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return Result.error("用户未登录");
        }
        
        Map<String, Object> userInfo = buildUserInfo(currentUser);
        return Result.success(userInfo);
    }

    /**
     * 刷新Token
     */
    @Operation(summary = "刷新Token", description = "刷新用户登录Token")
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        SysUser currentUser = UserContext.getCurrentUser();
        
        if (currentUser == null) {
            return Result.error("用户未登录");
        }
        
        // 生成新Token
        String newToken = sysUserService.login(currentUser.getUsername(), "");
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", newToken);
        data.put("userInfo", buildUserInfo(currentUser));
        
        return Result.success("Token刷新成功", data);
    }

    /**
     * 构建用户信息
     */
    private Map<String, Object> buildUserInfo(SysUser user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("realName", user.getRealName());
        userInfo.put("email", user.getEmail());
        userInfo.put("phone", user.getPhone());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("gender", user.getGender());
        userInfo.put("status", user.getStatus());
        
        // TODO: 添加角色和权限信息
        userInfo.put("roles", new String[]{"admin"});
        userInfo.put("permissions", new String[]{"user:view", "user:add", "user:edit", "user:delete"});
        
        return userInfo;
    }
}
