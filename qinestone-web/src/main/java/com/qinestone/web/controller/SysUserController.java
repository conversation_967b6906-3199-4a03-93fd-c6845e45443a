package com.qinestone.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qinestone.common.annotation.RequireLogin;
import com.qinestone.common.annotation.RequirePermission;
import com.qinestone.common.annotation.ResponseResult;
import com.qinestone.common.result.Result;
import com.qinestone.entity.dto.UserCreateDTO;
import com.qinestone.entity.system.SysUser;
import com.qinestone.entity.vo.UserVO;
import com.qinestone.service.system.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统用户控制器
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Tag(name = "用户管理", description = "系统用户管理相关接口")
@RestController
@RequestMapping("/api/system/user")
@ResponseResult
@RequireLogin
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 分页查询用户列表
     */
    @Operation(summary = "分页查询用户列表", description = "根据条件分页查询用户信息")
    @GetMapping("/page")
    @RequirePermission("user:view")
    public Result<IPage<UserVO>> pageUsers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "真实姓名") @RequestParam(required = false) String realName,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<SysUser> page = new Page<>(current, size);
        IPage<SysUser> userPage = sysUserService.pageUsers(page, username, realName, status);
        
        // 转换为VO
        IPage<UserVO> voPage = userPage.convert(user -> {
            UserVO vo = BeanUtil.copyProperties(user, UserVO.class);
            // 隐藏密码
            return vo;
        });
        
        return Result.success(voPage);
    }

    /**
     * 根据ID查询用户详情
     */
    @Operation(summary = "查询用户详情", description = "根据用户ID查询用户详细信息")
    @GetMapping("/{id}")
    @RequirePermission("user:view")
    public Result<UserVO> getUserById(@Parameter(description = "用户ID") @PathVariable Long id) {
        SysUser user = sysUserService.getById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        UserVO vo = BeanUtil.copyProperties(user, UserVO.class);
        return Result.success(vo);
    }

    /**
     * 创建用户
     */
    @Operation(summary = "创建用户", description = "创建新的系统用户")
    @PostMapping
    @RequirePermission("user:add")
    public Result<Void> createUser(@Validated @RequestBody UserCreateDTO createDTO) {
        SysUser user = BeanUtil.copyProperties(createDTO, SysUser.class);
        boolean success = sysUserService.createUser(user);
        
        return success ? Result.success("用户创建成功") : Result.error("用户创建失败");
    }

    /**
     * 更新用户
     */
    @Operation(summary = "更新用户", description = "更新用户信息")
    @PutMapping("/{id}")
    @RequirePermission("user:edit")
    public Result<Void> updateUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Validated @RequestBody UserCreateDTO updateDTO) {
        
        SysUser user = BeanUtil.copyProperties(updateDTO, SysUser.class);
        user.setId(id);
        boolean success = sysUserService.updateUser(user);
        
        return success ? Result.success("用户更新成功") : Result.error("用户更新失败");
    }

    /**
     * 删除用户
     */
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @DeleteMapping("/{id}")
    @RequirePermission("user:delete")
    public Result<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        boolean success = sysUserService.deleteUser(id);
        return success ? Result.success("用户删除成功") : Result.error("用户删除失败");
    }

    /**
     * 批量删除用户
     */
    @Operation(summary = "批量删除用户", description = "根据用户ID列表批量删除用户")
    @DeleteMapping("/batch")
    @RequirePermission("user:delete")
    public Result<Void> batchDeleteUsers(@RequestBody Long[] userIds) {
        boolean success = sysUserService.batchDeleteUsers(userIds);
        return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
    }

    /**
     * 重置用户密码
     */
    @Operation(summary = "重置用户密码", description = "重置指定用户的密码")
    @PutMapping("/{id}/password")
    @RequirePermission("user:edit")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "新密码") @RequestParam String newPassword) {
        
        boolean success = sysUserService.resetPassword(id, newPassword);
        return success ? Result.success("密码重置成功") : Result.error("密码重置失败");
    }

    /**
     * 修改用户状态
     */
    @Operation(summary = "修改用户状态", description = "启用或禁用用户")
    @PutMapping("/{id}/status")
    @RequirePermission("user:edit")
    public Result<Void> changeStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "状态") @RequestParam Integer status) {
        
        boolean success = sysUserService.changeStatus(id, status);
        return success ? Result.success("状态修改成功") : Result.error("状态修改失败");
    }

    /**
     * 检查用户名是否存在
     */
    @Operation(summary = "检查用户名", description = "检查用户名是否已存在")
    @GetMapping("/check/username")
    public Result<Boolean> checkUsername(
            @Parameter(description = "用户名") @RequestParam String username,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        
        boolean exists = sysUserService.checkUsernameExists(username, userId);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已存在")
    @GetMapping("/check/email")
    public Result<Boolean> checkEmail(
            @Parameter(description = "邮箱") @RequestParam String email,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        
        boolean exists = sysUserService.checkEmailExists(email, userId);
        return Result.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @Operation(summary = "检查手机号", description = "检查手机号是否已存在")
    @GetMapping("/check/phone")
    public Result<Boolean> checkPhone(
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        
        boolean exists = sysUserService.checkPhoneExists(phone, userId);
        return Result.success(exists);
    }
}
