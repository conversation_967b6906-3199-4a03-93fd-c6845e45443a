package com.qinestone.web.interceptor;

import com.qinestone.common.context.UserContext;
import com.qinestone.common.utils.TokenUtils;
import com.qinestone.entity.system.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 认证拦截器
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenUtils tokenUtils;

    /**
     * Token请求头名称
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取Token
        String token = request.getHeader(AUTHORIZATION_HEADER);
        
        if (token != null) {
            // 根据Token获取用户信息
            SysUser user = tokenUtils.getUserByToken(token);
            if (user != null) {
                // 设置当前用户到上下文
                UserContext.setCurrentUser(user);
                // 刷新Token过期时间
                tokenUtils.refreshToken(token);
                log.debug("用户认证成功: {}", user.getUsername());
            }
        }
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除用户上下文
        UserContext.clear();
    }
}
