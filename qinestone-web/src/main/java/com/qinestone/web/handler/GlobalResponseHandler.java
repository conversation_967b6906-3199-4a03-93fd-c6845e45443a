package com.qinestone.web.handler;

import com.qinestone.common.annotation.ResponseResult;
import com.qinestone.common.result.Result;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应处理器 - 统一返回值包装
 * 
 * <AUTHOR> 4.0 sonnet
 */
@RestControllerAdvice
public class GlobalResponseHandler implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查类或方法是否有@ResponseResult注解
        return hasResponseResultAnnotation(returnType);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        
        // 如果已经是Result类型，直接返回
        if (body instanceof Result) {
            return body;
        }
        
        // 如果是String类型，需要特殊处理
        if (body instanceof String) {
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            return Result.success(body);
        }
        
        // 包装成统一返回格式
        return Result.success(body);
    }

    /**
     * 检查是否有@ResponseResult注解
     */
    private boolean hasResponseResultAnnotation(MethodParameter returnType) {
        // 检查方法上的注解
        ResponseResult methodAnnotation = returnType.getMethodAnnotation(ResponseResult.class);
        if (methodAnnotation != null) {
            return methodAnnotation.value();
        }
        
        // 检查类上的注解
        ResponseResult classAnnotation = returnType.getDeclaringClass().getAnnotation(ResponseResult.class);
        if (classAnnotation != null) {
            return classAnnotation.value();
        }
        
        // 默认包装
        return true;
    }
}
