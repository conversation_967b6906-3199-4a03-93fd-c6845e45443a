package com.qinestone.common.aspect;

import com.qinestone.common.annotation.RequirePermission;
import com.qinestone.common.annotation.RequireRole;
import com.qinestone.common.context.UserContext;
import com.qinestone.common.exception.BusinessException;
import com.qinestone.common.result.ResultCode;
import com.qinestone.entity.system.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * 权限校验切面
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Aspect
@Component
@Order(2)
public class PermissionAspect {

    /**
     * 定义切点：所有标注了权限注解的方法
     */
    @Pointcut("@annotation(com.qinestone.common.annotation.RequirePermission) || @within(com.qinestone.common.annotation.RequirePermission) || " +
              "@annotation(com.qinestone.common.annotation.RequireRole) || @within(com.qinestone.common.annotation.RequireRole)")
    public void permissionPointcut() {
    }

    /**
     * 环绕通知：检查权限
     */
    @Around("permissionPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取当前用户
        SysUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            log.warn("用户未登录，无法进行权限校验，方法: {}.{}", method.getDeclaringClass().getSimpleName(), method.getName());
            throw new BusinessException(ResultCode.NOT_LOGIN);
        }
        
        // 检查权限注解
        checkPermission(method, currentUser);
        
        // 检查角色注解
        checkRole(method, currentUser);
        
        return joinPoint.proceed();
    }

    /**
     * 检查权限
     */
    private void checkPermission(Method method, SysUser user) {
        RequirePermission methodAnnotation = method.getAnnotation(RequirePermission.class);
        RequirePermission classAnnotation = method.getDeclaringClass().getAnnotation(RequirePermission.class);
        
        RequirePermission annotation = methodAnnotation != null ? methodAnnotation : classAnnotation;
        if (annotation == null || annotation.value().length == 0) {
            return;
        }
        
        String[] requiredPermissions = annotation.value();
        RequirePermission.LogicType logicType = annotation.logical();
        
        // TODO: 从数据库或缓存中获取用户权限列表
        List<String> userPermissions = getUserPermissions(user.getId());
        
        boolean hasPermission = false;
        if (logicType == RequirePermission.LogicType.AND) {
            // 必须具有所有权限
            hasPermission = userPermissions.containsAll(Arrays.asList(requiredPermissions));
        } else {
            // 只需具有其中一个权限
            hasPermission = Arrays.stream(requiredPermissions)
                    .anyMatch(userPermissions::contains);
        }
        
        if (!hasPermission) {
            log.warn("用户权限不足，用户: {}, 需要权限: {}, 拥有权限: {}, 方法: {}.{}", 
                    user.getUsername(), 
                    Arrays.toString(requiredPermissions), 
                    userPermissions,
                    method.getDeclaringClass().getSimpleName(), 
                    method.getName());
            throw new BusinessException(ResultCode.NO_PERMISSION);
        }
        
        log.debug("权限校验通过，用户: {}, 权限: {}", user.getUsername(), Arrays.toString(requiredPermissions));
    }

    /**
     * 检查角色
     */
    private void checkRole(Method method, SysUser user) {
        RequireRole methodAnnotation = method.getAnnotation(RequireRole.class);
        RequireRole classAnnotation = method.getDeclaringClass().getAnnotation(RequireRole.class);
        
        RequireRole annotation = methodAnnotation != null ? methodAnnotation : classAnnotation;
        if (annotation == null || annotation.value().length == 0) {
            return;
        }
        
        String[] requiredRoles = annotation.value();
        RequireRole.LogicType logicType = annotation.logical();
        
        // TODO: 从数据库或缓存中获取用户角色列表
        List<String> userRoles = getUserRoles(user.getId());
        
        boolean hasRole = false;
        if (logicType == RequireRole.LogicType.AND) {
            // 必须具有所有角色
            hasRole = userRoles.containsAll(Arrays.asList(requiredRoles));
        } else {
            // 只需具有其中一个角色
            hasRole = Arrays.stream(requiredRoles)
                    .anyMatch(userRoles::contains);
        }
        
        if (!hasRole) {
            log.warn("用户角色不足，用户: {}, 需要角色: {}, 拥有角色: {}, 方法: {}.{}", 
                    user.getUsername(), 
                    Arrays.toString(requiredRoles), 
                    userRoles,
                    method.getDeclaringClass().getSimpleName(), 
                    method.getName());
            throw new BusinessException(ResultCode.NO_PERMISSION);
        }
        
        log.debug("角色校验通过，用户: {}, 角色: {}", user.getUsername(), Arrays.toString(requiredRoles));
    }

    /**
     * 获取用户权限列表
     * TODO: 实现从数据库或缓存中获取用户权限
     */
    private List<String> getUserPermissions(Long userId) {
        // 临时返回空列表，实际应该从数据库查询
        return Arrays.asList("user:view", "user:add", "user:edit", "user:delete");
    }

    /**
     * 获取用户角色列表
     * TODO: 实现从数据库或缓存中获取用户角色
     */
    private List<String> getUserRoles(Long userId) {
        // 临时返回空列表，实际应该从数据库查询
        return Arrays.asList("admin", "user");
    }
}
