package com.qinestone.common.aspect;

import com.qinestone.common.annotation.RequireLogin;
import com.qinestone.common.context.UserContext;
import com.qinestone.common.exception.BusinessException;
import com.qinestone.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 登录认证切面
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class LoginAspect {

    /**
     * 定义切点：所有标注了@RequireLogin注解的方法
     */
    @Pointcut("@annotation(com.qinestone.common.annotation.RequireLogin) || @within(com.qinestone.common.annotation.RequireLogin)")
    public void loginPointcut() {
    }

    /**
     * 环绕通知：检查登录状态
     */
    @Around("loginPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 检查方法上的注解
        RequireLogin methodAnnotation = method.getAnnotation(RequireLogin.class);
        RequireLogin classAnnotation = method.getDeclaringClass().getAnnotation(RequireLogin.class);
        
        boolean requireLogin = true;
        
        // 方法注解优先级高于类注解
        if (methodAnnotation != null) {
            requireLogin = methodAnnotation.value();
        } else if (classAnnotation != null) {
            requireLogin = classAnnotation.value();
        }
        
        // 如果需要登录，检查登录状态
        if (requireLogin) {
            checkLogin(method);
        }
        
        return joinPoint.proceed();
    }

    /**
     * 检查登录状态
     */
    private void checkLogin(Method method) {
        if (!UserContext.isLogin()) {
            log.warn("用户未登录，访问方法: {}.{}", method.getDeclaringClass().getSimpleName(), method.getName());
            throw new BusinessException(ResultCode.NOT_LOGIN);
        }
        
        log.debug("用户已登录，当前用户: {}, 访问方法: {}.{}", 
                UserContext.getCurrentUsername(), 
                method.getDeclaringClass().getSimpleName(), 
                method.getName());
    }
}
