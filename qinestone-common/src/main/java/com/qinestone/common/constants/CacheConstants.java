package com.qinestone.common.constants;

/**
 * 缓存常量类
 * 
 * <AUTHOR> 4.0 sonnet
 */
public class CacheConstants {

    /**
     * 缓存键前缀
     */
    public static final String CACHE_PREFIX = "qinestone:";

    /**
     * 用户缓存前缀
     */
    public static final String USER_CACHE_PREFIX = CACHE_PREFIX + "user:";

    /**
     * 登录用户缓存前缀
     */
    public static final String LOGIN_USER_PREFIX = CACHE_PREFIX + "login:user:";

    /**
     * 验证码缓存前缀
     */
    public static final String CAPTCHA_PREFIX = CACHE_PREFIX + "captcha:";

    /**
     * 权限缓存前缀
     */
    public static final String PERMISSION_PREFIX = CACHE_PREFIX + "permission:";

    /**
     * 角色缓存前缀
     */
    public static final String ROLE_PREFIX = CACHE_PREFIX + "role:";

    /**
     * 菜单缓存前缀
     */
    public static final String MENU_PREFIX = CACHE_PREFIX + "menu:";

    /**
     * 字典缓存前缀
     */
    public static final String DICT_PREFIX = CACHE_PREFIX + "dict:";

    /**
     * 配置缓存前缀
     */
    public static final String CONFIG_PREFIX = CACHE_PREFIX + "config:";

    /**
     * 限流缓存前缀
     */
    public static final String RATE_LIMIT_PREFIX = CACHE_PREFIX + "rate_limit:";

    /**
     * 分布式锁前缀
     */
    public static final String LOCK_PREFIX = CACHE_PREFIX + "lock:";

    /**
     * 默认缓存过期时间（秒）
     */
    public static final long DEFAULT_EXPIRE_TIME = 3600L;

    /**
     * 登录用户缓存过期时间（秒）
     */
    public static final long LOGIN_USER_EXPIRE_TIME = 7200L;

    /**
     * 验证码过期时间（秒）
     */
    public static final long CAPTCHA_EXPIRE_TIME = 300L;

    /**
     * 权限缓存过期时间（秒）
     */
    public static final long PERMISSION_EXPIRE_TIME = 1800L;

    /**
     * 字典缓存过期时间（秒）
     */
    public static final long DICT_EXPIRE_TIME = 86400L;
}
