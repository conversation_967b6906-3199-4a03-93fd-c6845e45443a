package com.qinestone.common.context;

import com.qinestone.entity.system.SysUser;

/**
 * 用户上下文工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
public class UserContext {

    private static final ThreadLocal<SysUser> USER_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 设置当前用户
     */
    public static void setCurrentUser(SysUser user) {
        USER_THREAD_LOCAL.set(user);
    }

    /**
     * 获取当前用户
     */
    public static SysUser getCurrentUser() {
        return USER_THREAD_LOCAL.get();
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        SysUser user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        SysUser user = getCurrentUser();
        return user != null ? user.getUsername() : null;
    }

    /**
     * 判断是否已登录
     */
    public static boolean isLogin() {
        return getCurrentUser() != null;
    }

    /**
     * 清除当前用户
     */
    public static void clear() {
        USER_THREAD_LOCAL.remove();
    }
}
