package com.qinestone.common.result;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 统一返回结果类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 返回消息
     */
    private String msg;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 返回数据
     */
    private T data;
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), true, null);
    }
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), true, data);
    }
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success(String msg, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), msg, true, data);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error() {
        return new Result<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMsg(), false, null);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error(String msg) {
        return new Result<>(ResultCode.ERROR.getCode(), msg, false, null);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error(Integer code, String msg) {
        return new Result<>(code, msg, false, null);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return new Result<>(resultCode.getCode(), resultCode.getMsg(), false, null);
    }
}
