package com.qinestone.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),
    
    /**
     * 失败
     */
    ERROR(500, "操作失败"),
    
    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),
    
    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),
    
    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),
    
    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    
    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    
    /**
     * 用户未登录
     */
    NOT_LOGIN(1001, "用户未登录"),
    
    /**
     * 用户名或密码错误
     */
    LOGIN_ERROR(1002, "用户名或密码错误"),
    
    /**
     * 权限不足
     */
    NO_PERMISSION(1003, "权限不足"),
    
    /**
     * 用户已存在
     */
    USER_EXIST(1004, "用户已存在"),
    
    /**
     * 用户不存在
     */
    USER_NOT_EXIST(1005, "用户不存在"),
    
    /**
     * 数据库操作失败
     */
    DATABASE_ERROR(2001, "数据库操作失败"),
    
    /**
     * Redis操作失败
     */
    REDIS_ERROR(2002, "Redis操作失败");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 消息
     */
    private final String msg;
}
