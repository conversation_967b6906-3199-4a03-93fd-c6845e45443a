package com.qinestone.common.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.qinestone.common.constants.CacheConstants;
import com.qinestone.entity.system.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Token工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class TokenUtils {

    @Autowired
    private RedisUtils redisUtils;

    /**
     * Token前缀
     */
    private static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 生成Token
     */
    public String generateToken(SysUser user) {
        // 生成唯一Token
        String token = IdUtil.fastSimpleUUID();
        
        // 将用户信息存储到Redis
        String key = CacheConstants.LOGIN_USER_PREFIX + token;
        redisUtils.set(key, user, CacheConstants.LOGIN_USER_EXPIRE_TIME, TimeUnit.SECONDS);
        
        return TOKEN_PREFIX + token;
    }

    /**
     * 根据Token获取用户信息
     */
    public SysUser getUserByToken(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        
        // 移除Token前缀
        if (token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }
        
        String key = CacheConstants.LOGIN_USER_PREFIX + token;
        return redisUtils.get(key, SysUser.class);
    }

    /**
     * 刷新Token过期时间
     */
    public void refreshToken(String token) {
        if (StrUtil.isBlank(token)) {
            return;
        }
        
        // 移除Token前缀
        if (token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }
        
        String key = CacheConstants.LOGIN_USER_PREFIX + token;
        if (redisUtils.hasKey(key)) {
            redisUtils.expire(key, CacheConstants.LOGIN_USER_EXPIRE_TIME, TimeUnit.SECONDS);
        }
    }

    /**
     * 删除Token
     */
    public void removeToken(String token) {
        if (StrUtil.isBlank(token)) {
            return;
        }
        
        // 移除Token前缀
        if (token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }
        
        String key = CacheConstants.LOGIN_USER_PREFIX + token;
        redisUtils.delete(key);
    }

    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        return getUserByToken(token) != null;
    }

    /**
     * 从Token中提取纯Token值
     */
    public String extractToken(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        
        if (token.startsWith(TOKEN_PREFIX)) {
            return token.substring(TOKEN_PREFIX.length());
        }
        
        return token;
    }
}
