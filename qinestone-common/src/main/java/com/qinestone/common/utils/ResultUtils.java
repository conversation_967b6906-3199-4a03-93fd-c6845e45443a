package com.qinestone.common.utils;

import com.qinestone.common.result.Result;
import com.qinestone.common.result.ResultCode;

/**
 * 返回结果工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
public class ResultUtils {

    /**
     * 成功返回
     */
    public static <T> Result<T> success() {
        return Result.success();
    }

    /**
     * 成功返回
     */
    public static <T> Result<T> success(T data) {
        return Result.success(data);
    }

    /**
     * 成功返回
     */
    public static <T> Result<T> success(String msg, T data) {
        return Result.success(msg, data);
    }

    /**
     * 失败返回
     */
    public static <T> Result<T> error() {
        return Result.error();
    }

    /**
     * 失败返回
     */
    public static <T> Result<T> error(String msg) {
        return Result.error(msg);
    }

    /**
     * 失败返回
     */
    public static <T> Result<T> error(Integer code, String msg) {
        return Result.error(code, msg);
    }

    /**
     * 失败返回
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return Result.error(resultCode);
    }

    /**
     * 根据条件返回成功或失败
     */
    public static <T> Result<T> result(boolean condition) {
        return condition ? success() : error();
    }

    /**
     * 根据条件返回成功或失败
     */
    public static <T> Result<T> result(boolean condition, T data) {
        return condition ? success(data) : error();
    }

    /**
     * 根据条件返回成功或失败
     */
    public static <T> Result<T> result(boolean condition, String successMsg, String errorMsg) {
        return condition ? success(successMsg, null) : error(errorMsg);
    }

    /**
     * 根据条件返回成功或失败
     */
    public static <T> Result<T> result(boolean condition, T data, String errorMsg) {
        return condition ? success(data) : error(errorMsg);
    }

    /**
     * 判断结果是否成功
     */
    public static boolean isSuccess(Result<?> result) {
        return result != null && Boolean.TRUE.equals(result.getSuccess());
    }

    /**
     * 判断结果是否失败
     */
    public static boolean isError(Result<?> result) {
        return !isSuccess(result);
    }

    /**
     * 获取结果数据
     */
    public static <T> T getData(Result<T> result) {
        return isSuccess(result) ? result.getData() : null;
    }

    /**
     * 获取错误信息
     */
    public static String getErrorMsg(Result<?> result) {
        return isError(result) ? result.getMsg() : null;
    }
}
