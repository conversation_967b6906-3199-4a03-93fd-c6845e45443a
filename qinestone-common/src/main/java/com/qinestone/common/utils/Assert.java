package com.qinestone.common.utils;

import com.qinestone.common.exception.BusinessException;
import com.qinestone.common.result.ResultCode;
import cn.hutool.core.util.StrUtil;

import java.util.Collection;

/**
 * 断言工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
public class Assert {

    /**
     * 断言对象不为空
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言对象不为空
     */
    public static void notNull(Object object, ResultCode resultCode) {
        if (object == null) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言字符串不为空
     */
    public static void notBlank(String str, String message) {
        if (StrUtil.isBlank(str)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言字符串不为空
     */
    public static void notBlank(String str, ResultCode resultCode) {
        if (StrUtil.isBlank(str)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, ResultCode resultCode) {
        if (collection == null || collection.isEmpty()) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, ResultCode resultCode) {
        if (array == null || array.length == 0) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言表达式为真
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言表达式为真
     */
    public static void isTrue(boolean expression, ResultCode resultCode) {
        if (!expression) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言表达式为假
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言表达式为假
     */
    public static void isFalse(boolean expression, ResultCode resultCode) {
        if (expression) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言两个对象相等
     */
    public static void equals(Object obj1, Object obj2, String message) {
        if (obj1 == null ? obj2 != null : !obj1.equals(obj2)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言两个对象相等
     */
    public static void equals(Object obj1, Object obj2, ResultCode resultCode) {
        if (obj1 == null ? obj2 != null : !obj1.equals(obj2)) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言数字大于0
     */
    public static void isPositive(Number number, String message) {
        if (number == null || number.doubleValue() <= 0) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言数字大于0
     */
    public static void isPositive(Number number, ResultCode resultCode) {
        if (number == null || number.doubleValue() <= 0) {
            throw new BusinessException(resultCode);
        }
    }

    /**
     * 断言数字大于等于0
     */
    public static void isNotNegative(Number number, String message) {
        if (number == null || number.doubleValue() < 0) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言数字大于等于0
     */
    public static void isNotNegative(Number number, ResultCode resultCode) {
        if (number == null || number.doubleValue() < 0) {
            throw new BusinessException(resultCode);
        }
    }
}
