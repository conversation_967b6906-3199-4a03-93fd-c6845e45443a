package com.qinestone.common.annotation;

import java.lang.annotation.*;

/**
 * 需要角色注解
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {
    
    /**
     * 角色码
     */
    String[] value() default {};
    
    /**
     * 角色逻辑类型
     */
    LogicType logical() default LogicType.AND;
    
    /**
     * 逻辑类型枚举
     */
    enum LogicType {
        /**
         * 必须具有所有角色
         */
        AND,
        /**
         * 只需具有其中一个角色
         */
        OR
    }
}
