package com.qinestone.common.annotation;

import java.lang.annotation.*;

/**
 * 需要权限注解
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {
    
    /**
     * 权限码
     */
    String[] value() default {};
    
    /**
     * 权限逻辑类型
     */
    LogicType logical() default LogicType.AND;
    
    /**
     * 逻辑类型枚举
     */
    enum LogicType {
        /**
         * 必须具有所有权限
         */
        AND,
        /**
         * 只需具有其中一个权限
         */
        OR
    }
}
