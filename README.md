# 青石教育管理系统

## 项目简介

青石教育管理系统是一个基于 Spring Boot 3 + MyBatis Plus + Redis + PostgreSQL 的多模块企业级后台管理系统。

## 技术栈

- **后端框架**: Spring Boot 3.2.5
- **Java版本**: JDK 21
- **数据库**: PostgreSQL 15+
- **ORM框架**: MyBatis Plus 3.5.6
- **缓存**: Redis 7.0+
- **连接池**: HikariCP
- **构建工具**: Maven 3.8+
- **API文档**: Knife4j (Swagger3)
- **工具类**: Hutool、FastJson2

## 项目结构

```
qinestone-edu/
├── qinestone-common/          # 公共模块
│   ├── annotation/           # 自定义注解
│   ├── aspect/              # AOP切面
│   ├── config/              # 配置类
│   ├── constants/           # 常量定义
│   ├── context/             # 上下文工具
│   ├── exception/           # 异常定义
│   ├── result/              # 统一返回结果
│   └── utils/               # 工具类
├── qinestone-entity/          # 实体模块
│   ├── base/                # 基础实体
│   ├── dto/                 # 数据传输对象
│   ├── system/              # 系统实体
│   └── vo/                  # 视图对象
├── qinestone-dao/             # 数据访问层
│   ├── config/              # 数据库配置
│   ├── mapper/              # Mapper接口
│   └── resources/mapper/    # XML映射文件
├── qinestone-service/         # 业务逻辑层
│   └── system/              # 系统服务
├── qinestone-web/             # Web控制层
│   ├── config/              # Web配置
│   ├── controller/          # 控制器
│   ├── handler/             # 处理器
│   └── interceptor/         # 拦截器
└── qinestone-admin/           # 启动模块
    ├── java/                # 启动类
    └── resources/           # 配置文件
```

## 核心功能

### 1. 统一返回值包装
- 自动包装所有API返回结果为统一格式：`{code, msg, success, data}`
- 支持通过 `@ResponseResult` 注解控制是否包装

### 2. 全局异常处理
- 统一处理业务异常、参数校验异常、系统异常等
- 自动转换为统一的错误响应格式

### 3. 登录认证切面
- 基于Token的用户认证机制
- 支持 `@RequireLogin` 注解进行登录校验
- 自动管理用户上下文信息

### 4. 权限校验切面
- 支持 `@RequirePermission` 和 `@RequireRole` 注解
- 灵活的权限逻辑控制（AND/OR）
- 基于AOP的权限拦截

### 5. Redis缓存集成
- 自定义Redis序列化配置
- 完善的缓存工具类
- 分布式会话管理

### 6. MyBatis Plus增强
- 自动填充创建时间、更新时间等字段
- 逻辑删除支持
- 分页插件配置

## 快速开始

### 1. 环境要求
- JDK 21+
- Maven 3.8+
- PostgreSQL 15+
- Redis 7.0+

### 2. 数据库初始化
```sql
-- 执行初始化脚本
\i qinestone-admin/src/main/resources/sql/init.sql
```

### 3. 配置修改
修改 `application.yml` 中的数据库和Redis连接信息：
```yaml
spring:
  datasource:
    url: **********************************************
    username: postgres
    password: 123456
  data:
    redis:
      host: localhost
      port: 6379
```

### 4. 启动应用
```bash
mvn clean install
cd qinestone-admin
mvn spring-boot:run
```

### 5. 访问应用
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/doc.html

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/userinfo` - 获取当前用户信息

### 用户管理接口
- `GET /api/system/user/page` - 分页查询用户
- `GET /api/system/user/{id}` - 查询用户详情
- `POST /api/system/user` - 创建用户
- `PUT /api/system/user/{id}` - 更新用户
- `DELETE /api/system/user/{id}` - 删除用户

## 开发指南

### 1. 添加新的业务模块
1. 在 `qinestone-entity` 中定义实体类、DTO、VO
2. 在 `qinestone-dao` 中创建Mapper接口和XML文件
3. 在 `qinestone-service` 中实现业务逻辑
4. 在 `qinestone-web` 中创建Controller

### 2. 使用注解
```java
@RestController
@RequireLogin  // 需要登录
@RequirePermission("user:view")  // 需要权限
public class UserController {
    
    @GetMapping("/list")
    @ResponseResult  // 自动包装返回结果
    public List<User> list() {
        return userService.list();
    }
}
```

### 3. 异常处理
```java
// 抛出业务异常
throw new BusinessException("用户不存在");
throw new BusinessException(ResultCode.USER_NOT_EXIST);

// 使用断言工具
Assert.notNull(user, "用户不能为空");
Assert.isTrue(user.getStatus() == 1, "用户已被禁用");
```

## 部署说明

### 1. 打包应用
```bash
mvn clean package -Pprod
```

### 2. Docker部署
```dockerfile
FROM openjdk:21-jre-slim
COPY qinestone-admin/target/qinestone-admin-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 作者: Claude 4.0 sonnet
- 邮箱: <EMAIL>
- 项目地址: https://github.com/qinestone/qinestone-edu
