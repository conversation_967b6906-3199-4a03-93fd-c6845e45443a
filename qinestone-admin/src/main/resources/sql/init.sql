-- 青石教育数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS qinestone_edu;

-- 使用数据库
\c qinestone_edu;

-- 创建系统用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(200) COMMENT '头像',
    gender INTEGER DEFAULT 1 COMMENT '性别 1-男 2-女',
    status INTEGER DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted INTEGER DEFAULT 0 COMMENT '逻辑删除标识 0-未删除 1-已删除'
);

-- 创建索引
CREATE INDEX idx_sys_user_username ON sys_user(username);
CREATE INDEX idx_sys_user_status ON sys_user(status);
CREATE INDEX idx_sys_user_deleted ON sys_user(deleted);

-- 插入初始管理员用户 (密码: admin123)
INSERT INTO sys_user (id, username, password, real_name, email, status, create_by, update_by) 
VALUES (1, 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOqImqwawBM07vYPfxPwnoQOFHe', '系统管理员', '<EMAIL>', 1, 1, 1)
ON CONFLICT (username) DO NOTHING;

-- 添加表注释
COMMENT ON TABLE sys_user IS '系统用户表';
COMMENT ON COLUMN sys_user.id IS '主键ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.avatar IS '头像';
COMMENT ON COLUMN sys_user.gender IS '性别 1-男 2-女';
COMMENT ON COLUMN sys_user.status IS '状态 1-启用 0-禁用';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.create_time IS '创建时间';
COMMENT ON COLUMN sys_user.update_time IS '更新时间';
COMMENT ON COLUMN sys_user.create_by IS '创建人';
COMMENT ON COLUMN sys_user.update_by IS '更新人';
COMMENT ON COLUMN sys_user.deleted IS '逻辑删除标识 0-未删除 1-已删除';
