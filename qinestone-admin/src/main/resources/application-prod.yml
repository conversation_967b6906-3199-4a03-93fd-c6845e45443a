# 生产环境配置
spring:
  # 数据源配置
  datasource:
    url: *********************************************************************************************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:your_password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:prod-redis-host}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:your_redis_password}
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 日志配置
logging:
  level:
    com.qinestone: info
    org.springframework.web: warn
    org.springframework.security: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/qinestone-edu.log
