package com.qinestone.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 青石教育管理后台启动类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@SpringBootApplication(scanBasePackages = "com.qinestone")
@MapperScan("com.qinestone.dao.mapper")
public class QinestoneEduApplication {

    public static void main(String[] args) {
        SpringApplication.run(QinestoneEduApplication.class, args);
        System.out.println("🎉 青石教育管理后台启动成功！");
        System.out.println("📖 API文档地址: http://localhost:8080/doc.html");
    }
}
