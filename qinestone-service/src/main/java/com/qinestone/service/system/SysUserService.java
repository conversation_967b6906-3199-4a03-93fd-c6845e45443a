package com.qinestone.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qinestone.entity.system.SysUser;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR> 4.0 sonnet
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getByUsername(String username);

    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 登录Token
     */
    String login(String username, String password);

    /**
     * 用户登出
     * 
     * @param token 登录Token
     */
    void logout(String token);

    /**
     * 分页查询用户列表
     * 
     * @param page     分页参数
     * @param username 用户名（可选）
     * @param realName 真实姓名（可选）
     * @param status   状态（可选）
     * @return 用户分页列表
     */
    IPage<SysUser> pageUsers(Page<SysUser> page, String username, String realName, Integer status);

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 是否创建成功
     */
    boolean createUser(SysUser user);

    /**
     * 更新用户
     * 
     * @param user 用户信息
     * @return 是否更新成功
     */
    boolean updateUser(SysUser user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteUsers(Long[] userIds);

    /**
     * 重置用户密码
     * 
     * @param userId      用户ID
     * @param newPassword 新密码
     * @return 是否重置成功
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 修改用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 是否修改成功
     */
    boolean changeStatus(Long userId, Integer status);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param userId   用户ID（排除自己）
     * @return 是否存在
     */
    boolean checkUsernameExists(String username, Long userId);

    /**
     * 检查邮箱是否存在
     * 
     * @param email  邮箱
     * @param userId 用户ID（排除自己）
     * @return 是否存在
     */
    boolean checkEmailExists(String email, Long userId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone  手机号
     * @param userId 用户ID（排除自己）
     * @return 是否存在
     */
    boolean checkPhoneExists(String phone, Long userId);
}
