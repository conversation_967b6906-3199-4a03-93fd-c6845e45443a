package com.qinestone.service.system.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qinestone.common.exception.BusinessException;
import com.qinestone.common.result.ResultCode;
import com.qinestone.common.utils.Assert;
import com.qinestone.common.utils.TokenUtils;
import com.qinestone.dao.mapper.SysUserMapper;
import com.qinestone.entity.system.SysUser;
import com.qinestone.service.system.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * 系统用户服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private TokenUtils tokenUtils;

    @Override
    public SysUser getByUsername(String username) {
        Assert.notBlank(username, "用户名不能为空");
        return baseMapper.selectByUsername(username);
    }

    @Override
    public String login(String username, String password) {
        Assert.notBlank(username, "用户名不能为空");
        Assert.notBlank(password, "密码不能为空");

        // 查询用户
        SysUser user = getByUsername(username);
        Assert.notNull(user, ResultCode.LOGIN_ERROR);

        // 检查用户状态
        Assert.isTrue(user.getStatus() == 1, "用户已被禁用");

        // 验证密码
        if (!BCrypt.checkpw(password, user.getPassword())) {
            throw new BusinessException(ResultCode.LOGIN_ERROR);
        }

        // 生成Token
        String token = tokenUtils.generateToken(user);
        log.info("用户登录成功: {}", username);
        return token;
    }

    @Override
    public void logout(String token) {
        if (StrUtil.isNotBlank(token)) {
            tokenUtils.removeToken(token);
            log.info("用户登出成功");
        }
    }

    @Override
    public IPage<SysUser> pageUsers(Page<SysUser> page, String username, String realName, Integer status) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(username), SysUser::getUsername, username)
                .like(StrUtil.isNotBlank(realName), SysUser::getRealName, realName)
                .eq(status != null, SysUser::getStatus, status)
                .orderByDesc(SysUser::getCreateTime);
        return baseMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(SysUser user) {
        Assert.notNull(user, "用户信息不能为空");
        Assert.notBlank(user.getUsername(), "用户名不能为空");
        Assert.notBlank(user.getPassword(), "密码不能为空");

        // 检查用户名是否存在
        Assert.isFalse(checkUsernameExists(user.getUsername(), null), "用户名已存在");

        // 检查邮箱是否存在
        if (StrUtil.isNotBlank(user.getEmail())) {
            Assert.isFalse(checkEmailExists(user.getEmail(), null), "邮箱已存在");
        }

        // 检查手机号是否存在
        if (StrUtil.isNotBlank(user.getPhone())) {
            Assert.isFalse(checkPhoneExists(user.getPhone(), null), "手机号已存在");
        }

        // 加密密码
        user.setPassword(BCrypt.hashpw(user.getPassword(), BCrypt.gensalt()));

        // 设置默认状态
        if (user.getStatus() == null) {
            user.setStatus(1);
        }

        boolean result = save(user);
        if (result) {
            log.info("创建用户成功: {}", user.getUsername());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser user) {
        Assert.notNull(user, "用户信息不能为空");
        Assert.notNull(user.getId(), "用户ID不能为空");

        // 检查用户是否存在
        SysUser existUser = getById(user.getId());
        Assert.notNull(existUser, "用户不存在");

        // 检查用户名是否存在
        if (StrUtil.isNotBlank(user.getUsername()) && !user.getUsername().equals(existUser.getUsername())) {
            Assert.isFalse(checkUsernameExists(user.getUsername(), user.getId()), "用户名已存在");
        }

        // 检查邮箱是否存在
        if (StrUtil.isNotBlank(user.getEmail()) && !user.getEmail().equals(existUser.getEmail())) {
            Assert.isFalse(checkEmailExists(user.getEmail(), user.getId()), "邮箱已存在");
        }

        // 检查手机号是否存在
        if (StrUtil.isNotBlank(user.getPhone()) && !user.getPhone().equals(existUser.getPhone())) {
            Assert.isFalse(checkPhoneExists(user.getPhone(), user.getId()), "手机号已存在");
        }

        // 如果修改了密码，需要加密
        if (StrUtil.isNotBlank(user.getPassword())) {
            user.setPassword(BCrypt.hashpw(user.getPassword(), BCrypt.gensalt()));
        } else {
            user.setPassword(null); // 不修改密码
        }

        boolean result = updateById(user);
        if (result) {
            log.info("更新用户成功: {}", user.getUsername());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        Assert.notNull(userId, "用户ID不能为空");

        SysUser user = getById(userId);
        Assert.notNull(user, "用户不存在");

        boolean result = removeById(userId);
        if (result) {
            log.info("删除用户成功: {}", user.getUsername());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteUsers(Long[] userIds) {
        Assert.notEmpty(userIds, "用户ID列表不能为空");

        boolean result = removeByIds(Arrays.asList(userIds));
        if (result) {
            log.info("批量删除用户成功，数量: {}", userIds.length);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        Assert.notNull(userId, "用户ID不能为空");
        Assert.notBlank(newPassword, "新密码不能为空");

        SysUser user = getById(userId);
        Assert.notNull(user, "用户不存在");

        user.setPassword(BCrypt.hashpw(newPassword, BCrypt.gensalt()));
        boolean result = updateById(user);
        if (result) {
            log.info("重置用户密码成功: {}", user.getUsername());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(Long userId, Integer status) {
        Assert.notNull(userId, "用户ID不能为空");
        Assert.notNull(status, "状态不能为空");

        SysUser user = getById(userId);
        Assert.notNull(user, "用户不存在");

        user.setStatus(status);
        boolean result = updateById(user);
        if (result) {
            log.info("修改用户状态成功: {}, 状态: {}", user.getUsername(), status);
        }
        return result;
    }

    @Override
    public boolean checkUsernameExists(String username, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public boolean checkEmailExists(String email, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getEmail, email);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public boolean checkPhoneExists(String phone, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getPhone, phone);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return count(wrapper) > 0;
    }
}
