package com.qinestone.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qinestone.entity.system.SysUser;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统用户Mapper接口
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectByUsername(String username);
}
