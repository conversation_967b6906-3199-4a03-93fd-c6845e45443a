<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qinestone</groupId>
        <artifactId>qinestone-edu</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>qinestone-entity</artifactId>
    <name>qinestone-entity</name>
    <description>实体模块 - 数据库实体类、DTO、VO等</description>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.qinestone</groupId>
            <artifactId>qinestone-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        
        <!-- 校验框架 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>

</project>
